export const ENDPOINTS = {
    USER_LOGIN: "/api/auth/login",
    FORGOT_PASSWORD: "/api/auth/forgot-password",
    VERIFY_FORGOT_PASSWORD: "/api/auth/verify-forgot-password",
    REFRESH_TOKEN: "/api/auth/refresh-token",
    VERIFY_EMAIL: "/api/auth/verify-email",
    RESEND_VERIFY_EMAIL: "/api/auth/resend-verify-email",
    LOGOUT: "/api/auth/logout",
    REGISTER: "/api/auth/register",
    COURSE_CATEGORIES: "/api/course-categories",
    COURSE_CATEGORIES_BY_ID: (id: string) => `/api/course-categories/${id}`,
    ALL_USERS: "/api/users",
    ACTIVE_USERS_BY_ID: (userId: string) => `/api/users/${userId}/activate`,
    DEACTIVE_USERS_BY_ID: (userId: string) => `/api/users/${userId}/deactivate`,
    // Mentor Application Request endpoints
    APPLICATION_REQUESTS: "/api/application-requests",
    APPLICATION_REQUESTS_CURRENT_USER: "/api/application-requests/current-user",
    APPLICATION_REQUESTS_BY_ID: (id: string) => `/api/application-requests/${id}`,
    APPLICATION_REQUESTS_REQUEST_UPDATE: (id: string) => `/api/application-requests/${id}/request-update`,
    APPLICATION_REQUESTS_APPROVE: (id: string) => `/api/application-requests/${id}/approve`,
    APPLICATION_REQUESTS_REJECT: (id: string) => `/api/application-requests/${id}/reject`,
}