import { APIRequestContext, APIResponse } from '@playwright/test';
import { ENDPOINTS } from '@core/const/endpoint';
import {
    CreateApplicationRequest,
    UpdateApplicationRequest,
    GetApplicationRequestsParams,
    RequestUpdateRequest,
    RejectApplicationRequest
} from '../../data-type/mentor-application-request.type';

export default class MentorApplicationRequestAPI {
    private request: APIRequestContext;

    constructor(request: APIRequestContext) {
        this.request = request;
    }

    /**
     * Create a new mentor application request
     * POST /api/application-requests
     */
    async createApplicationRequest(requestData: CreateApplicationRequest, documents?: Buffer[]): Promise<APIResponse> {
        const multipartData: Record<string, any> = {
            Description: requestData.Description,
            Education: requestData.Education,
            WorkExperience: requestData.WorkExperience,
            Certifications: requestData.Certifications,
            Status: 0 // Pending status
        };

        // Add documents if provided
        if (documents && documents.length > 0) {
            multipartData.ApplicationDocuments = documents.map((doc, index) => ({
                name: `document_${index}.pdf`,
                mimeType: 'application/pdf',
                buffer: doc
            }));
        }

        return await this.request.post(ENDPOINTS.APPLICATION_REQUESTS, {
            multipart: multipartData
        });
    }

    /**
     * Update an existing mentor application request
     * PUT /api/application-requests
     */
    async updateApplicationRequest(requestData: UpdateApplicationRequest, documents?: Buffer[]): Promise<APIResponse> {
        const multipartData: Record<string, any> = {
            Id: requestData.Id,
            Description: requestData.Description,
            Education: requestData.Education,
            WorkExperience: requestData.WorkExperience,
            Certifications: requestData.Certifications
        };

        // Add documents if provided
        if (documents && documents.length > 0) {
            multipartData.ApplicationDocuments = documents.map((doc, index) => ({
                name: `updated_document_${index}.pdf`,
                mimeType: 'application/pdf',
                buffer: doc
            }));
        } else if (requestData.ApplicationDocuments) {
            multipartData.ApplicationDocuments = requestData.ApplicationDocuments;
        }

        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS, {
            multipart: multipartData
        });
    }

    /**
     * Get application requests with pagination and filters
     * GET /api/application-requests
     */
    async getApplicationRequests(params: GetApplicationRequestsParams): Promise<APIResponse> {
        const queryParams: Record<string, any> = {
            PageSize: params.PageSize.toString(),
            PageNumber: params.PageNumber.toString(),
            ApplicationRequestStatuses: params.ApplicationRequestStatuses.map(status => status.toString())
        };

        if (params.Search) {
            queryParams.Search = params.Search;
        }

        return await this.request.get(ENDPOINTS.APPLICATION_REQUESTS, {
            params: queryParams
        });
    }

    /**
     * Get current user's application request
     * GET /api/application-requests/current-user
     */
    async getCurrentUserApplicationRequest(): Promise<APIResponse> {
        return await this.request.get(ENDPOINTS.APPLICATION_REQUESTS_CURRENT_USER);
    }

    /**
     * Get application request by ID
     * GET /api/application-requests/{id}
     */
    async getApplicationRequestById(id: string): Promise<APIResponse> {
        return await this.request.get(ENDPOINTS.APPLICATION_REQUESTS_BY_ID(id));
    }

    /**
     * Request update for an application request
     * PUT /api/application-requests/{id}/request-update
     */
    async requestUpdateApplicationRequest(id: string, requestData: RequestUpdateRequest): Promise<APIResponse> {
        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS_REQUEST_UPDATE(id), {
            data: requestData
        });
    }

    /**
     * Approve an application request
     * PUT /api/application-requests/{id}/approve
     */
    async approveApplicationRequest(id: string): Promise<APIResponse> {
        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS_APPROVE(id));
    }

    /**
     * Reject an application request
     * PUT /api/application-requests/{id}/reject
     */
    async rejectApplicationRequest(id: string, requestData: RejectApplicationRequest): Promise<APIResponse> {
        return await this.request.put(ENDPOINTS.APPLICATION_REQUESTS_REJECT(id), {
            data: requestData
        });
    }
}
