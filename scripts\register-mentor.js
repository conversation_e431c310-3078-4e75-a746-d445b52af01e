const { chromium } = require('@playwright/test');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to prompt user for input
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// Helper function to prompt for password (plain text for testing)
function askPassword(question) {
    return askQuestion(question);
}

// Simple API call helper
async function makeAPICall(request, endpoint, data = null, isMultipart = false) {
    const options = {};

    if (data) {
        if (isMultipart) {
            options.multipart = data;
        } else {
            options.data = data;
        }
    }

    return await request.post(endpoint, options);
}

async function registerMentor() {
    console.log('🚀 Mentor Registration Script');
    console.log('===============================\n');

    try {
        // Step 1: Get user input
        console.log('📝 Step 1: Collect Registration Information');
        console.log('-------------------------------------------');
        
        const email = await askQuestion('Enter email address: ');
        const password = await askPassword('Enter password: ');
        const fullName = await askQuestion('Enter full name: ');
        const bio = await askQuestion('Enter bio (optional): ');

        console.log('\n✅ Information collected successfully!\n');

        // Step 2: Create browser context and register user
        console.log('🔄 Step 2: Registering User Account');
        console.log('-----------------------------------');

        const browser = await chromium.launch();
        const context = await browser.newContext();
        const request = context.request;

        // Prepare registration data for mentor (Role: 1)
        const registrationData = {
            Email: email,
            Password: password,
            FullName: fullName,
            Role: '1', // Mentor role as string for multipart
            Bio: bio || 'Experienced mentor ready to help others grow.',
            Expertises: [
                "365d9e94-51fc-4840-acfe-7f1432007c29",
                "8a5bc300-21c4-47d0-bb33-27d0a709d417"
            ],
            ProfessionalSkill: "Software Development",
            Experience: "5+ years of professional experience",
            CommunicationPreference: '1',
            Goals: "Help others achieve their learning goals",
            CourseCategoryIds: ["f47ac10b-58cc-4372-a567-0e02b2c3d479"],
            SessionFrequency: '2',
            Duration: '60',
            LearningStyle: '1',
            TeachingStyles: ['1', '2']
        };

        // Send registration request
        console.log('📤 Sending registration request...');
        const registerResponse = await makeAPICall(
            request,
            '/api/auth/register',
            registrationData,
            true
        );
        const registerResponseText = await registerResponse.text();
        
        console.log(`📊 Registration Response Status: ${registerResponse.status()}`);
        console.log(`📄 Registration Response: ${registerResponseText}`);

        if (!registerResponse.ok()) {
            console.error('❌ Registration failed!');
            console.error(`Status: ${registerResponse.status()}`);
            console.error(`Response: ${registerResponseText}`);
            await browser.close();
            rl.close();
            return;
        }

        console.log('✅ Registration request sent successfully!');
        console.log('📧 A verification email has been sent to your email address.\n');

        // Step 3: Get verification code from user
        console.log('📧 Step 3: Email Verification');
        console.log('-----------------------------');
        console.log('Please check your email and enter the verification code below.');
        
        const verificationCode = await askQuestion('Enter verification code from email: ');

        // Send email verification request
        console.log('\n🔄 Verifying email...');
        const verifyData = {
            email: email,
            code: verificationCode
        };

        const verifyResponse = await makeAPICall(
            request,
            '/api/auth/verify-email',
            verifyData
        );
        const verifyResponseText = await verifyResponse.text();
        
        console.log(`📊 Verification Response Status: ${verifyResponse.status()}`);
        console.log(`📄 Verification Response: ${verifyResponseText}`);

        if (!verifyResponse.ok()) {
            console.error('❌ Email verification failed!');
            console.error(`Status: ${verifyResponse.status()}`);
            console.error(`Response: ${verifyResponseText}`);
            
            // Offer to resend verification email
            const resend = await askQuestion('\nWould you like to resend the verification email? (y/n): ');
            if (resend.toLowerCase() === 'y' || resend.toLowerCase() === 'yes') {
                console.log('📤 Resending verification email...');
                const resendResponse = await makeAPICall(
                    request,
                    '/api/auth/resend-verify-email',
                    { email: email }
                );
                const resendResponseText = await resendResponse.text();
                console.log(`📊 Resend Response Status: ${resendResponse.status()}`);
                console.log(`📄 Resend Response: ${resendResponseText}`);
                
                if (resendResponse.ok()) {
                    console.log('✅ Verification email resent! Please check your email again.');
                    const newCode = await askQuestion('Enter new verification code: ');
                    const newVerifyData = {
                        email: email,
                        code: newCode
                    };
                    const newVerifyResponse = await makeAPICall(
                        request,
                        '/api/auth/verify-email',
                        newVerifyData
                    );
                    const newVerifyResponseText = await newVerifyResponse.text();
                    console.log(`📊 New Verification Response Status: ${newVerifyResponse.status()}`);
                    console.log(`📄 New Verification Response: ${newVerifyResponseText}`);
                    
                    if (!newVerifyResponse.ok()) {
                        console.error('❌ Email verification failed again!');
                        await browser.close();
                        rl.close();
                        return;
                    }
                } else {
                    console.error('❌ Failed to resend verification email!');
                    await browser.close();
                    rl.close();
                    return;
                }
            } else {
                await browser.close();
                rl.close();
                return;
            }
        }

        console.log('✅ Email verified successfully!\n');

        // Step 4: Test login to confirm registration is complete
        console.log('🔐 Step 4: Testing Login');
        console.log('------------------------');
        console.log('Testing login to confirm registration is complete...');
        
        const loginResponse = await makeAPICall(
            request,
            '/api/auth/login',
            { email: email, password: password }
        );
        const loginResponseText = await loginResponse.text();
        
        console.log(`📊 Login Response Status: ${loginResponse.status()}`);
        console.log(`📄 Login Response: ${loginResponseText}`);

        if (loginResponse.ok()) {
            console.log('🎉 SUCCESS! Mentor registration completed successfully!');
            console.log('===============================================');
            console.log(`📧 Email: ${email}`);
            console.log(`👤 Full Name: ${fullName}`);
            console.log(`🎯 Role: Mentor`);
            console.log('✅ Account is ready to use for testing!');
            
            // Parse and display access token if available
            try {
                const loginData = JSON.parse(loginResponseText);
                if (loginData.data && loginData.data.accessToken) {
                    console.log(`🔑 Access Token: ${loginData.data.accessToken.substring(0, 50)}...`);
                }
            } catch (e) {
                // Ignore JSON parsing errors
            }
        } else {
            console.error('❌ Login test failed! Registration may not be complete.');
            console.error(`Status: ${loginResponse.status()}`);
            console.error(`Response: ${loginResponseText}`);
        }

        await browser.close();
        rl.close();

    } catch (error) {
        console.error('💥 An error occurred during registration:');
        console.error(error);
        rl.close();
    }
}

// Run the registration script
registerMentor().catch(console.error);
