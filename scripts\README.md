# Mentor Registration Script

This script automates the 3-step mentor registration process for testing purposes.

## Overview

The mentor registration process consists of:

1. **Step 1**: Register user account with basic information
2. **Step 2**: Verify email address with code sent to email
3. **Step 3**: Test login to confirm registration is complete

## Prerequisites

- Node.js installed
- Playwright installed (`npm install @playwright/test`)
- Access to the email account you'll use for registration

## Usage

### Option 1: JavaScript Version (Recommended)

```bash
npm run register-mentor
```

### Option 2: TypeScript Version

```bash
npm run register-mentor-ts
```

### Manual Execution

You can also run the scripts directly:

```bash
# JavaScript version
node scripts/register-mentor.js

# TypeScript version (requires ts-node)
npx ts-node scripts/register-mentor.ts
```

## Script Flow

### 1. Information Collection
The script will prompt you for:
- **Email address**: Must be a valid email you have access to
- **Password**: Will be hidden as you type (use backspace to correct)
- **Full name**: Display name for the mentor account
- **Bio**: Optional description (will use default if empty)

### 2. Registration
- Sends registration request to `/api/auth/register`
- Creates mentor account with Role: 1
- Includes default expertise IDs and preferences
- Triggers verification email to be sent

### 3. Email Verification
- Prompts for verification code from email
- Sends verification request to `/api/auth/verify-email`
- Offers to resend verification email if first attempt fails
- Allows retry with new code

### 4. Login Test
- Tests login with registered credentials
- Confirms registration is complete
- Displays access token if successful

## Sample Output

```
🚀 Mentor Registration Script
===============================

📝 Step 1: Collect Registration Information
-------------------------------------------
Enter email address: <EMAIL>
Enter password: ********
Enter full name: John Mentor
Enter bio (optional): Experienced software developer

✅ Information collected successfully!

🔄 Step 2: Registering User Account
-----------------------------------
📤 Sending registration request...
📊 Registration Response Status: 200
📄 Registration Response: {"message":"Registration successful"}
✅ Registration request sent successfully!
📧 A verification email has been sent to your email address.

📧 Step 3: Email Verification
-----------------------------
Please check your email and enter the verification code below.
Enter verification code from email: 123456

🔄 Verifying email...
📊 Verification Response Status: 200
📄 Verification Response: {"message":"Email verified successfully"}
✅ Email verified successfully!

🔐 Step 4: Testing Login
------------------------
Testing login to confirm registration is complete...
📊 Login Response Status: 200
📄 Login Response: {"data":{"accessToken":"eyJ..."}}
🎉 SUCCESS! Mentor registration completed successfully!
===============================================
📧 Email: <EMAIL>
👤 Full Name: John Mentor
🎯 Role: Mentor
✅ Account is ready to use for testing!
🔑 Access Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM...
```

## Default Mentor Configuration

The script creates mentors with these default settings:
- **Role**: 1 (Mentor)
- **Expertises**: Default expertise IDs
- **Professional Skill**: "Software Development"
- **Experience**: "5+ years of professional experience"
- **Communication Preference**: 1
- **Goals**: "Help others achieve their learning goals"
- **Session Frequency**: 2
- **Duration**: 60 minutes
- **Learning Style**: 1
- **Teaching Styles**: [1, 2]

## Error Handling

The script handles common errors:
- Invalid email format
- Registration failures
- Email verification failures
- Login test failures
- Network errors

If email verification fails, the script offers to:
- Resend the verification email
- Retry with a new code

## Troubleshooting

### Registration Fails
- Check email format is valid
- Ensure email doesn't already exist
- Verify API endpoints are accessible

### Email Verification Fails
- Check spam/junk folder for verification email
- Ensure code is entered correctly (6 digits)
- Try resending verification email
- Check if email was already verified

### Login Test Fails
- Verify password was entered correctly
- Check if additional verification steps are required
- Ensure registration process completed successfully

## Security Notes

- Password input is hidden (shows asterisks)
- Access tokens are only partially displayed
- No sensitive data is logged to files
- Browser context is properly closed after use

## Integration with Tests

After successful registration, you can use the created mentor account in your API tests:

```javascript
// Use the registered mentor credentials in your tests
const mentorCredentials = {
    email: "<EMAIL>",
    password: "your-password"
};
```

The registered mentor will have all required fields populated and can be used immediately for testing mentor application requests and other mentor-specific functionality.
