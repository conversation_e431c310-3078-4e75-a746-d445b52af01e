// Application Request Status enum
export enum ApplicationRequestStatus {
    Pending = 0,
    UnderReview = 1,
    Approved = 2,
    Rejected = 3
}

// Application Document interface
export interface ApplicationDocument {
    filePath: string;
    fileName: string;
    fileContent?: string;
}

// Create Request interface
export interface CreateApplicationRequest {
    Description: string;
    Education: string;
    WorkExperience: string;
    Certifications: string[];
    ApplicationDocuments?: File[];
}

// Update Request interface
export interface UpdateApplicationRequest {
    Id: string;
    Description: string;
    Education: string;
    WorkExperience: string;
    Certifications: string[];
    ApplicationDocuments?: ApplicationDocument[];
}

// Get Requests query parameters
export interface GetApplicationRequestsParams {
    PageSize: number;
    PageNumber: number;
    Search?: string;
    ApplicationRequestStatuses: number[];
}

// Request Update interface
export interface RequestUpdateRequest {
    note: string;
}

// Reject Request interface
export interface RejectApplicationRequest {
    note: string;
}

// Application Request Response interface
export interface ApplicationRequestResponse {
    id: string;
    education: string;
    workExperience: string;
    fullName: string;
    description: string;
    status: number;
    summitted: string;
}

// Detailed Application Request Response interface
export interface DetailedApplicationRequestResponse {
    note: string;
    applicationRequestDocuments: ApplicationDocument[];
    mentorEmail: string;
    mentorExpertises: number[];
    mentorCertifications: string;
    avatarUrl: string;
    id: string;
    education: string;
    workExperience: string;
    fullName: string;
    description: string;
    status: number;
    summitted: string;
}

// Test data interfaces
export interface MentorCredentials {
    email: string;
    password: string;
}

export interface CreateRequestTestData {
    mentorCredentials: MentorCredentials;
    validRequest: CreateApplicationRequest;
    expectedResponse: {
        message: string;
        status: number;
    };
}

export interface UpdateRequestTestData {
    mentorCredentials: MentorCredentials;
    validUpdate: UpdateApplicationRequest;
    expectedResponse: {
        message: string;
        status: number;
    };
}

export interface GetRequestsTestData {
    adminCredentials: MentorCredentials;
    queryParams: GetApplicationRequestsParams[];
    expectedFields: string[];
}

export interface RequestUpdateTestData {
    adminCredentials: MentorCredentials;
    requestId: string;
    updateRequest: RequestUpdateRequest;
    expectedResponse: {
        message: string;
        status: number;
    };
}

export interface ApproveRequestTestData {
    adminCredentials: MentorCredentials;
    requestId: string;
    expectedResponse: {
        message: string;
        status: number;
    };
}

export interface RejectRequestTestData {
    adminCredentials: MentorCredentials;
    requestId: string;
    rejectRequest: RejectApplicationRequest;
    expectedResponse: {
        message: string;
        status: number;
    };
}

export interface GetRequestByIdTestData {
    adminCredentials: MentorCredentials;
    requestId: string;
    expectedFields: string[];
}

export interface MentorApplicationRequestTestData {
    createRequest: CreateRequestTestData;
    updateRequest: UpdateRequestTestData;
    getRequests: GetRequestsTestData;
    requestUpdate: RequestUpdateTestData;
    approveRequest: ApproveRequestTestData;
    rejectRequest: RejectRequestTestData;
    getRequestById: GetRequestByIdTestData;
}
