{"createRequest": {"mentorCredentials": {"email": "<EMAIL>", "password": "mentor123A@"}, "validRequest": {"Description": "I am passionate about mentoring and helping others grow in their careers. I have extensive experience in software development and would love to share my knowledge.", "Education": "Bachelor of Computer Science from MIT University", "WorkExperience": "10 years of experience at Google as Senior Software Engineer, leading teams and mentoring junior developers", "Certifications": ["AWS Certified Solutions Architect", "Google Cloud Professional Developer", "Scrum Master Certification"]}, "expectedResponse": {"message": "Create successfully", "status": 200}}, "updateRequest": {"mentorCredentials": {"email": "<EMAIL>", "password": "mentor123A@"}, "validUpdate": {"Id": "123e4567-e89b-12d3-a456-426614174000", "Description": "Updated description: I am passionate about mentoring with additional focus on AI and machine learning", "Education": "Master of Computer Science from Stanford University", "WorkExperience": "12 years of experience at Microsoft and Google, specializing in AI/ML and team leadership", "Certifications": ["AWS Certified Solutions Architect", "Google Cloud Professional Developer", "Machine Learning Engineer Certification", "Agile Coach Certification"], "ApplicationDocuments": [{"filePath": "/documents/updated-resume.pdf", "fileName": "updated-resume.pdf", "fileContent": "base64encodedcontent"}]}, "expectedResponse": {"message": "Update successfully", "status": 200}}, "getRequests": {"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "queryParams": [{"PageSize": 10, "PageNumber": 1, "ApplicationRequestStatuses": [0, 1, 2, 3]}, {"PageSize": 5, "PageNumber": 1, "Search": "mentor", "ApplicationRequestStatuses": [0, 1]}, {"PageSize": 20, "PageNumber": 2, "ApplicationRequestStatuses": [2]}], "expectedFields": ["id", "education", "workExperience", "fullName", "description", "status", "summitted"]}, "requestUpdate": {"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "requestId": "123e4567-e89b-12d3-a456-426614174001", "updateRequest": {"note": "Please provide more details about your teaching methodology and update your certifications section."}, "expectedResponse": {"message": "Request update sent successfully", "status": 200}}, "approveRequest": {"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "requestId": "123e4567-e89b-12d3-a456-426614174002", "expectedResponse": {"message": "Request approved successfully", "status": 200}}, "rejectRequest": {"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "requestId": "123e4567-e89b-12d3-a456-426614174003", "rejectRequest": {"note": "Application does not meet the minimum requirements. Please ensure you have at least 5 years of relevant experience and proper certifications."}, "expectedResponse": {"message": "Request rejected successfully", "status": 200}}, "getRequestById": {"adminCredentials": {"email": "<EMAIL>", "password": "admin123A@"}, "requestId": "123e4567-e89b-12d3-a456-426614174004", "expectedFields": ["note", "applicationRequestDocuments", "mentorEmail", "mentorExpertises", "mentorCertifications", "avatarUrl", "id", "education", "workExperience", "fullName", "description", "status", "summitted"]}, "errorScenarios": {"createRequest": {"invalidData": {"Description": "", "Education": "", "WorkExperience": "", "Certifications": []}, "expectedError": {"status": 400, "message": "Validation failed"}}, "updateRequest": {"notFound": {"Id": "00000000-0000-0000-0000-000000000000", "expectedError": {"status": 404, "message": "Application request not found"}}, "cannotUpdate": {"Id": "123e4567-e89b-12d3-a456-426614174005", "expectedError": {"status": 400, "message": "<PERSON><PERSON> cannot update request that is not under review"}}}, "requestUpdate": {"notFound": {"requestId": "00000000-0000-0000-0000-000000000000", "expectedError": {"status": 404, "message": "Application request not found"}}, "alreadyUnderReview": {"requestId": "123e4567-e89b-12d3-a456-426614174006", "expectedError": {"status": 400, "message": "Admin cannot request update request that is under review"}}}, "approveRequest": {"notFound": {"requestId": "00000000-0000-0000-0000-000000000000", "expectedError": {"status": 404, "message": "Application request not found"}}, "underReview": {"requestId": "123e4567-e89b-12d3-a456-426614174007", "expectedError": {"status": 400, "message": "Admin cannot approve request that is under review"}}}, "rejectRequest": {"notFound": {"requestId": "00000000-0000-0000-0000-000000000000", "expectedError": {"status": 404, "message": "Application request not found"}}, "underReview": {"requestId": "123e4567-e89b-12d3-a456-426614174008", "expectedError": {"status": 400, "message": "Admin cannot reject request that is under review"}}, "alreadyApproved": {"requestId": "123e4567-e89b-12d3-a456-426614174009", "expectedError": {"status": 400, "message": "Cannot reject approved request"}}}}}